<template>
  <view class="knowledge-container">
    <!-- 搜索栏 -->
    <view class="search-section">
      <view class="search-bar">
        <text class="search-icon">🔍</text>
        <input 
          class="search-input"
          placeholder="搜索技术文档、操作指南..."
          v-model="searchKeyword"
          @input="onSearchInput"
        />
        <text v-if="searchKeyword" class="clear-btn" @click="clearSearch">✕</text>
      </view>
    </view>
    
    <!-- 快速入口 -->
    <view class="quick-entry">
      <view class="entry-grid">
        <view 
          class="entry-item"
          v-for="entry in quickEntries"
          :key="entry.key"
          @click="handleEntryClick(entry)"
        >
          <view class="entry-icon" :style="{ backgroundColor: entry.color }">
            <text class="icon-text">{{ entry.icon }}</text>
          </view>
          <text class="entry-label">{{ entry.label }}</text>
          <text class="entry-count">{{ entry.count }}篇</text>
        </view>
      </view>
    </view>
    
    <!-- 分类导航 -->
    <view class="category-section">
      <view class="section-title">📚 知识分类</view>
      <scroll-view class="category-scroll" scroll-x>
        <view class="category-tabs">
          <view 
            class="category-tab"
            :class="{ active: currentCategory === category.key }"
            v-for="category in categories"
            :key="category.key"
            @click="changeCategory(category.key)"
          >
            <text class="tab-text">{{ category.label }}</text>
          </view>
        </view>
      </scroll-view>
    </view>
    
    <!-- 内容列表 -->
    <scroll-view 
      class="content-scroll"
      scroll-y
      @scrolltolower="loadMore"
      :refresher-enabled="true"
      :refresher-triggered="isRefreshing"
      @refresherrefresh="onRefresh"
    >
      <view class="content-list">
        <!-- 推荐内容 -->
        <view v-if="currentCategory === 'all' && !searchKeyword" class="recommend-section">
          <view class="section-title">⭐ 推荐内容</view>
          <view class="recommend-list">
            <view 
              class="recommend-item"
              v-for="item in recommendItems"
              :key="item.id"
              @click="viewContent(item)"
            >
              <image class="recommend-image" :src="item.cover" mode="aspectFill" />
              <view class="recommend-content">
                <text class="recommend-title">{{ item.title }}</text>
                <text class="recommend-desc">{{ item.description }}</text>
                <view class="recommend-meta">
                  <text class="meta-tag">{{ item.category }}</text>
                  <text class="meta-views">{{ item.views }}次阅读</text>
                </view>
              </view>
            </view>
          </view>
        </view>
        
        <!-- 文档列表 -->
        <view class="document-list">
          <view 
            class="document-item"
            v-for="doc in filteredDocuments"
            :key="doc.id"
            @click="viewContent(doc)"
          >
            <view class="doc-icon">
              <text class="icon-text">{{ getDocIcon(doc.type) }}</text>
            </view>
            <view class="doc-content">
              <text class="doc-title">{{ doc.title }}</text>
              <text class="doc-desc">{{ doc.description }}</text>
              <view class="doc-meta">
                <text class="meta-category">{{ doc.categoryName }}</text>
                <text class="meta-time">{{ formatTime(doc.updateTime) }}</text>
                <text class="meta-views">{{ doc.views }}次阅读</text>
              </view>
            </view>
            <view class="doc-actions">
              <view class="action-btn" @click.stop="toggleFavorite(doc)">
                <text class="action-icon">{{ doc.isFavorite ? '❤️' : '🤍' }}</text>
              </view>
              <view class="action-btn" @click.stop="shareContent(doc)">
                <text class="action-icon">📤</text>
              </view>
            </view>
          </view>
        </view>
        
        <!-- 加载状态 -->
        <view v-if="isLoading" class="loading-state">
          <text class="loading-text">加载中...</text>
        </view>
        
        <!-- 空状态 -->
        <view v-if="!isLoading && filteredDocuments.length === 0" class="empty-state">
          <text class="empty-icon">📖</text>
          <text class="empty-text">{{ getEmptyText() }}</text>
          <text class="empty-desc">{{ getEmptyDesc() }}</text>
        </view>
        
        <!-- 没有更多数据 -->
        <view v-if="!hasMore && filteredDocuments.length > 0" class="no-more">
          <text class="no-more-text">没有更多内容了</text>
        </view>
      </view>
    </scroll-view>
    
    <!-- 悬浮操作按钮 -->
    <view class="fab" @click="showFabMenu">
      <text class="fab-icon">{{ fabMenuVisible ? '✕' : '➕' }}</text>
    </view>
    
    <!-- 悬浮菜单 -->
    <view class="fab-menu" v-if="fabMenuVisible" @click="hideFabMenu">
      <view class="fab-menu-item" @click.stop="addFeedback">
        <text class="fab-menu-icon">💭</text>
        <text class="fab-menu-text">意见反馈</text>
      </view>
      <view class="fab-menu-item" @click.stop="requestContent">
        <text class="fab-menu-icon">📝</text>
        <text class="fab-menu-text">内容建议</text>
      </view>
      <view class="fab-menu-item" @click.stop="viewFavorites">
        <text class="fab-menu-icon">❤️</text>
        <text class="fab-menu-text">我的收藏</text>
      </view>
    </view>
  </view>
</template>

<script>
import { knowledgeApi } from '@/api/index.js'
import { formatRelativeTime, showToast, debounce } from '@/utils/index.js'

export default {
  data() {
    return {
      searchKeyword: '',
      currentCategory: 'all',
      isLoading: false,
      isRefreshing: false,
      hasMore: true,
      page: 1,
      pageSize: 10,
      documents: [],
      fabMenuVisible: false,
      quickEntries: [
        { key: 'manual', label: '技术手册', icon: '📖', color: '#2196F3', count: 45 },
        { key: 'guide', label: '操作指南', icon: '📋', color: '#4CAF50', count: 32 },
        { key: 'faq', label: '常见问题', icon: '❓', color: '#FF9800', count: 28 },
        { key: 'video', label: '视频教程', icon: '🎥', color: '#9C27B0', count: 15 }
      ],
      categories: [
        { key: 'all', label: '全部' },
        { key: 'installation', label: '安装' },
        { key: 'maintenance', label: '维护' },
        { key: 'repair', label: '维修' },
        { key: 'inspection', label: '巡检' },
        { key: 'safety', label: '安全' },
        { key: 'troubleshooting', label: '故障排除' }
      ],
      recommendItems: [
        {
          id: 'rec1',
          title: '燃气表安装标准操作流程',
          description: '详细介绍燃气表安装的标准流程和注意事项',
          cover: '/static/images/manual-cover-1.jpg',
          category: '安装指南',
          views: 1250
        },
        {
          id: 'rec2',
          title: '常见故障快速诊断手册',
          description: '汇总常见设备故障的快速诊断方法',
          cover: '/static/images/manual-cover-2.jpg',
          category: '故障排除',
          views: 980
        }
      ]
    }
  },
  
  computed: {
    filteredDocuments() {
      let result = this.documents
      
      // 分类筛选
      if (this.currentCategory !== 'all') {
        result = result.filter(doc => doc.category === this.currentCategory)
      }
      
      // 搜索筛选
      if (this.searchKeyword) {
        const keyword = this.searchKeyword.toLowerCase()
        result = result.filter(doc => 
          doc.title.toLowerCase().includes(keyword) ||
          doc.description.toLowerCase().includes(keyword) ||
          doc.content.toLowerCase().includes(keyword)
        )
      }
      
      return result
    }
  },
  
  onLoad() {
    this.loadDocuments()
  },
  
  onShow() {
    // 页面显示时可以刷新收藏状态等
  },
  
  onPullDownRefresh() {
    this.onRefresh()
  },
  
  onReachBottom() {
    this.loadMore()
  },
  
  methods: {
    // 搜索输入处理（防抖）
    onSearchInput: debounce(function() {
      this.page = 1
      this.loadDocuments(true)
    }, 500),
    
    // 清除搜索
    clearSearch() {
      this.searchKeyword = ''
      this.page = 1
      this.loadDocuments(true)
    },
    
    // 处理快速入口点击
    handleEntryClick(entry) {
      switch (entry.key) {
        case 'manual':
          this.currentCategory = 'manual'
          break
        case 'guide':
          this.currentCategory = 'guide'
          break
        case 'faq':
          uni.navigateTo({
            url: '/pages/knowledge/faq'
          })
          return
        case 'video':
          uni.navigateTo({
            url: '/pages/knowledge/videos'
          })
          return
      }
      this.page = 1
      this.loadDocuments(true)
    },
    
    // 切换分类
    changeCategory(category) {
      this.currentCategory = category
      this.page = 1
      this.loadDocuments(true)
    },
    
    // 加载文档列表
    async loadDocuments(reset = false) {
      if (this.isLoading) return
      
      this.isLoading = true
      
      try {
        const params = {
          page: reset ? 1 : this.page,
          pageSize: this.pageSize,
          category: this.currentCategory === 'all' ? undefined : this.currentCategory,
          keyword: this.searchKeyword
        }
        
        const result = await knowledgeApi.getDocuments(params)
        
        if (reset) {
          this.documents = result.list
          this.page = 1
        } else {
          this.documents = [...this.documents, ...result.list]
        }
        
        this.hasMore = result.list.length === this.pageSize
        
      } catch (error) {
        showToast('加载失败')
      } finally {
        this.isLoading = false
        this.isRefreshing = false
      }
    },
    
    // 下拉刷新
    onRefresh() {
      this.isRefreshing = true
      this.page = 1
      this.loadDocuments(true)
    },
    
    // 加载更多
    loadMore() {
      if (this.hasMore && !this.isLoading) {
        this.page++
        this.loadDocuments()
      }
    },
    
    // 获取文档图标
    getDocIcon(type) {
      const iconMap = {
        manual: '📖',
        guide: '📋',
        faq: '❓',
        video: '🎥',
        document: '📄'
      }
      return iconMap[type] || '📄'
    },
    
    // 格式化时间
    formatTime(time) {
      return formatRelativeTime(time)
    },
    
    // 获取空状态文本
    getEmptyText() {
      if (this.searchKeyword) return '没有找到相关内容'
      if (this.currentCategory !== 'all') return '该分类暂无内容'
      return '暂无知识库内容'
    },
    
    // 获取空状态描述
    getEmptyDesc() {
      if (this.searchKeyword) return '尝试使用其他关键词搜索'
      return '内容正在完善中，敬请期待'
    },
    
    // 查看内容
    viewContent(item) {
      uni.navigateTo({
        url: `/pages/knowledge/content-detail?id=${item.id}`
      })
    },
    
    // 切换收藏状态
    async toggleFavorite(doc) {
      try {
        if (doc.isFavorite) {
          await knowledgeApi.removeFavorite(doc.id)
          doc.isFavorite = false
          showToast('已取消收藏')
        } else {
          await knowledgeApi.addFavorite(doc.id)
          doc.isFavorite = true
          showToast('已添加收藏')
        }
      } catch (error) {
        showToast('操作失败')
      }
    },
    
    // 分享内容
    shareContent(doc) {
      // #ifdef MP-WEIXIN
      uni.showShareMenu({
        withShareTicket: true
      })
      // #endif
      
      // #ifndef MP-WEIXIN
      uni.share({
        provider: 'weixin',
        scene: 'WXSceneSession',
        type: 0,
        href: `https://example.com/knowledge/${doc.id}`,
        title: doc.title,
        summary: doc.description,
        imageUrl: doc.cover
      })
      // #endif
    },
    
    // 显示悬浮菜单
    showFabMenu() {
      this.fabMenuVisible = !this.fabMenuVisible
    },
    
    // 隐藏悬浮菜单
    hideFabMenu() {
      this.fabMenuVisible = false
    },
    
    // 添加反馈
    addFeedback() {
      this.fabMenuVisible = false
      uni.navigateTo({
        url: '/pages/feedback/feedback'
      })
    },
    
    // 请求内容
    requestContent() {
      this.fabMenuVisible = false
      uni.navigateTo({
        url: '/pages/knowledge/request-content'
      })
    },
    
    // 查看收藏
    viewFavorites() {
      this.fabMenuVisible = false
      uni.navigateTo({
        url: '/pages/knowledge/favorites'
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.knowledge-container {
  min-height: 100vh;
  background: #f5f5f5;
  display: flex;
  flex-direction: column;
}

.search-section {
  background: white;
  padding: $uni-spacing-base $uni-spacing-lg;
  border-bottom: 1rpx solid #f0f0f0;
}

.search-bar {
  display: flex;
  align-items: center;
  background: #f8f9fa;
  border-radius: $uni-border-radius-lg;
  padding: 0 $uni-spacing-base;
  height: 80rpx;
}

.search-icon {
  font-size: $uni-font-size-lg;
  color: #999;
  margin-right: $uni-spacing-sm;
}

.search-input {
  flex: 1;
  font-size: $uni-font-size-base;
  border: none;
  background: transparent;
}

.clear-btn {
  font-size: $uni-font-size-base;
  color: #999;
  padding: $uni-spacing-xs;
}

.quick-entry {
  background: white;
  margin: $uni-spacing-base $uni-spacing-lg;
  border-radius: $uni-border-radius-lg;
  padding: $uni-spacing-lg;
  box-shadow: $shadow-sm;
}

.entry-grid {
  display: flex;
  gap: $uni-spacing-lg;
}

.entry-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: $uni-spacing-sm;
}

.entry-icon {
  width: 100rpx;
  height: 100rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.icon-text {
  font-size: $uni-font-size-xl;
  color: white;
}

.entry-label {
  font-size: $uni-font-size-base;
  color: $uni-text-color;
}

.entry-count {
  font-size: $uni-font-size-sm;
  color: $uni-text-color-secondary;
}

.category-section {
  background: white;
  padding: $uni-spacing-base 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.section-title {
  font-size: $uni-font-size-lg;
  font-weight: 500;
  color: $uni-text-color;
  padding: 0 $uni-spacing-lg;
  margin-bottom: $uni-spacing-base;
}

.category-scroll {
  height: 80rpx;
}

.category-tabs {
  display: flex;
  padding: 0 $uni-spacing-lg;
  gap: $uni-spacing-lg;
}

.category-tab {
  padding: $uni-spacing-sm $uni-spacing-base;
  border-radius: $uni-border-radius-lg;
  background: #f8f9fa;
  white-space: nowrap;
  
  &.active {
    background: $uni-color-primary;
    
    .tab-text {
      color: white;
    }
  }
}

.tab-text {
  font-size: $uni-font-size-base;
  color: $uni-text-color;
}

.content-scroll {
  flex: 1;
}

.content-list {
  padding: $uni-spacing-base $uni-spacing-lg;
}

.recommend-section {
  margin-bottom: $uni-spacing-lg;
}

.recommend-list {
  background: white;
  border-radius: $uni-border-radius-lg;
  overflow: hidden;
  box-shadow: $shadow-sm;
}

.recommend-item {
  display: flex;
  padding: $uni-spacing-lg;
  border-bottom: 1rpx solid #f0f0f0;
  
  &:last-child {
    border-bottom: none;
  }
}

.recommend-image {
  width: 160rpx;
  height: 120rpx;
  border-radius: $uni-border-radius-base;
  margin-right: $uni-spacing-base;
  flex-shrink: 0;
}

.recommend-content {
  flex: 1;
}

.recommend-title {
  font-size: $uni-font-size-lg;
  font-weight: 500;
  color: $uni-text-color;
  margin-bottom: $uni-spacing-xs;
}

.recommend-desc {
  font-size: $uni-font-size-sm;
  color: $uni-text-color-secondary;
  line-height: 1.4;
  margin-bottom: $uni-spacing-sm;
}

.recommend-meta {
  display: flex;
  gap: $uni-spacing-base;
}

.meta-tag {
  font-size: $uni-font-size-sm;
  color: $uni-color-primary;
  background: rgba($uni-color-primary, 0.1);
  padding: 4rpx 8rpx;
  border-radius: 8rpx;
}

.meta-views {
  font-size: $uni-font-size-sm;
  color: $uni-text-color-secondary;
}

.document-list {
  background: white;
  border-radius: $uni-border-radius-lg;
  overflow: hidden;
  box-shadow: $shadow-sm;
}

.document-item {
  display: flex;
  align-items: center;
  padding: $uni-spacing-lg;
  border-bottom: 1rpx solid #f0f0f0;
  
  &:last-child {
    border-bottom: none;
  }
  
  &:active {
    background: #f8f9fa;
  }
}

.doc-icon {
  width: 80rpx;
  height: 80rpx;
  background: #f8f9fa;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: $uni-spacing-base;
  flex-shrink: 0;
}

.doc-content {
  flex: 1;
}

.doc-title {
  font-size: $uni-font-size-base;
  font-weight: 500;
  color: $uni-text-color;
  margin-bottom: $uni-spacing-xs;
}

.doc-desc {
  font-size: $uni-font-size-sm;
  color: $uni-text-color-secondary;
  line-height: 1.4;
  margin-bottom: $uni-spacing-sm;
}

.doc-meta {
  display: flex;
  gap: $uni-spacing-base;
}

.meta-category {
  font-size: $uni-font-size-sm;
  color: $uni-color-primary;
  background: rgba($uni-color-primary, 0.1);
  padding: 4rpx 8rpx;
  border-radius: 8rpx;
}

.meta-time, .meta-views {
  font-size: $uni-font-size-sm;
  color: $uni-text-color-secondary;
}

.doc-actions {
  display: flex;
  gap: $uni-spacing-sm;
}

.action-btn {
  width: 64rpx;
  height: 64rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f8f9fa;
  border-radius: 50%;
}

.action-icon {
  font-size: $uni-font-size-lg;
}

.loading-state, .empty-state, .no-more {
  text-align: center;
  padding: $uni-spacing-2xl;
}

.loading-text, .no-more-text {
  font-size: $uni-font-size-base;
  color: $uni-text-color-secondary;
}

.empty-icon {
  display: block;
  font-size: 120rpx;
  margin-bottom: $uni-spacing-base;
  opacity: 0.3;
}

.empty-text {
  font-size: $uni-font-size-lg;
  color: $uni-text-color;
  margin-bottom: $uni-spacing-xs;
}

.empty-desc {
  font-size: $uni-font-size-base;
  color: $uni-text-color-secondary;
}

.fab {
  position: fixed;
  right: $uni-spacing-lg;
  bottom: 200rpx;
  width: 120rpx;
  height: 120rpx;
  background: $uni-color-primary;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: $shadow-lg;
  z-index: 100;
}

.fab-icon {
  font-size: $uni-font-size-xl;
  color: white;
  font-weight: bold;
}

.fab-menu {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.3);
  z-index: 99;
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  justify-content: flex-end;
  padding: $uni-spacing-lg;
  padding-bottom: 340rpx;
}

.fab-menu-item {
  display: flex;
  align-items: center;
  gap: $uni-spacing-base;
  background: white;
  padding: $uni-spacing-base $uni-spacing-lg;
  border-radius: $uni-border-radius-lg;
  margin-bottom: $uni-spacing-base;
  box-shadow: $shadow-base;
}

.fab-menu-icon {
  font-size: $uni-font-size-lg;
}

.fab-menu-text {
  font-size: $uni-font-size-base;
  color: $uni-text-color;
}
</style>
