<template>
  <view class="task-list-container">
    <!-- 搜索栏 -->
    <view class="search-section">
      <view class="search-bar">
        <uni-icons type="search" size="18" color="#bbb"></uni-icons>
        <input
          class="search-input"
          placeholder="搜索工单号、客户、地址"
          v-model="searchKeyword"
          @input="onSearchInput"
        />
        <uni-icons v-if="searchKeyword" type="clear" size="16" color="#ccc" @click="clearSearch"></uni-icons>
      </view>
    </view>
    
    <!-- 筛选标签 -->
    <view class="filter-section">
      <view class="filter-row">
        <!-- 状态筛选 -->
        <view class="status-filter" @click="toggleFilterExpand">
          <view class="current-filter-content">
            <text class="current-filter-text">{{ getCurrentFilterText() }}</text>
            <view v-if="getCurrentFilterCount() > 0" class="current-filter-badge">
              {{ getCurrentFilterCount() }}
            </view>
          </view>
          <view class="expand-icon" :class="{ expanded: isFilterExpanded }">
            <uni-icons type="down" size="14" color="#888"></uni-icons>
          </view>
        </view>



        <!-- 高级筛选按钮 -->
        <view class="filter-actions">
          <view class="filter-btn" @click="showFilterModal">
            <uni-icons type="settings-filled" size="18" color="#fff"></uni-icons>
          </view>
        </view>
      </view>

      <!-- 状态筛选下拉选项 -->
      <view class="filter-dropdown" :class="{ show: isFilterExpanded }">
        <view class="filter-options">
          <view
            class="filter-option"
            :class="{ active: currentFilter === filter.value }"
            v-for="filter in filterTabs"
            :key="filter.value"
            @click="selectFilter(filter.value)"
          >
            <text class="option-text">{{ filter.label }}</text>
            <view v-if="filter.count > 0" class="option-badge">{{ filter.count }}</view>
          </view>
        </view>
      </view>


    </view>
    
    <!-- 遮罩层 - 点击关闭下拉菜单 -->
    <view v-if="isFilterExpanded" class="filter-mask" @click="closeAllDropdowns"></view>

    <!-- 任务列表 -->
    <view class="task-scroll">
      <view class="task-list">
        <view
          class="task-card"
          v-for="task in filteredTasks"
          :key="task.id"
          @click="goToTaskDetail(task)"
        >
          <!-- 任务头部 -->
          <view class="task-header">
            <view class="task-header-left">
              <text class="task-number">{{ task.taskNumber }}</text>
              <view class="task-badges">
                <view class="task-status-badge" :class="'status-' + task.status">
                  <text class="status-text">{{ getStatusText(task.status) }}</text>
                </view>
                <view class="task-type-badge" :class="'type-' + task.type">
                  <text class="type-text">{{ getTypeText(task.type) }}</text>
                </view>
              </view>
            </view>
            <view class="task-header-right">
              <text class="task-time">{{ formatTime(task.createTime) }}</text>
              <text v-if="task.distanceText" class="task-distance">{{ task.distanceText }}</text>
            </view>
          </view>
          
          <!-- 任务主要信息 -->
          <view class="task-main">
            <text class="task-title">{{ task.title }}</text>
            <view class="task-customer-row">
              <view class="customer-info">
                <uni-icons type="contact-filled" size="16" color="#666"></uni-icons>
                <text class="task-customer">{{ task.customerName }}</text>
              </view>
              <text class="task-phone" @click.stop="callCustomer(task)">
                <uni-icons type="phone-filled" size="14" color="#4c6ef5"></uni-icons>
                {{ task.customerPhone }}
              </text>
            </view>
            <view class="task-address" @click.stop="openNavigation(task)">
              <view class="address-content">
                <uni-icons type="location-filled" size="16" color="#ff6b6b"></uni-icons>
                <view class="address-info">
                  <text class="address-main">{{ task.address }}</text>
                  <text class="address-detail" v-if="task.detailAddress">{{ task.detailAddress }}</text>
                </view>
              </view>
              <uni-icons type="right" size="14" color="#ccc"></uni-icons>
            </view>
          </view>

          <!-- 任务操作 - 底部右边 -->
          <view class="task-actions" v-if="task.status === 'new' || task.status === 'accepted'">
            <button
              v-if="task.status === 'new'"
              class="action-btn primary"
              @click.stop="acceptTask(task)"
            >
              接单
            </button>
            <button
              v-if="task.status === 'accepted'"
              class="action-btn success"
              @click.stop="startTask(task)"
            >
              开始
            </button>
          </view>
        </view>
        
        <!-- 加载状态 -->
        <view v-if="isLoading" class="loading-state">
          <text class="loading-text">加载中...</text>
        </view>
        
        <!-- 空状态 -->
        <view v-if="!isLoading && filteredTasks.length === 0" class="empty-state">
          <uni-icons type="list" size="80" color="#e0e0e0"></uni-icons>
          <text class="empty-text">暂无任务</text>
          <text class="empty-desc">{{ getEmptyDesc() }}</text>
        </view>
        
        <!-- 没有更多数据 -->
        <view v-if="!hasMore && filteredTasks.length > 0" class="no-more">
          <text class="no-more-text">没有更多数据了</text>
        </view>
      </view>
    </view>
    
    <!-- 筛选弹窗 -->
    <view v-if="showFilter" class="filter-modal" @click="hideFilterModal">
      <view class="filter-content" @click.stop>
        <view class="filter-header">
          <text class="filter-title">筛选条件</text>
          <uni-icons type="closeempty" size="20" color="#666" @click="hideFilterModal"></uni-icons>
        </view>
        
        <view class="filter-body">
          <!-- 任务类型 -->
          <view class="filter-group">
            <text class="group-title">任务类型</text>
            <view class="option-list">
              <view
                class="option-item"
                :class="{ active: filterOptions.type.includes(type.value) }"
                v-for="type in taskTypes"
                :key="type.value"
                @click="toggleFilterOption('type', type.value)"
              >
                <text class="option-text">{{ type.label }}</text>
              </view>
            </view>
          </view>

          <!-- 距离排序 -->
          <view class="filter-group">
            <text class="group-title">距离排序</text>
            <view class="option-list">
              <view
                class="option-item"
                :class="{ active: currentDistanceSort === option.value }"
                v-for="option in distanceSortOptions"
                :key="option.value"
                @click="selectDistanceSort(option.value)"
              >
                <text class="option-text">{{ option.label }}</text>
              </view>
            </view>
          </view>

        </view>
        
        <view class="filter-footer">
          <button class="filter-reset" @click="resetFilter">重置</button>
          <button class="filter-confirm" @click="applyFilter">确定</button>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  name: 'TaskList',
  data() {
    return {
      searchKeyword: '',
      currentFilter: 'all',
      currentDistanceSort: 'default',
      showFilter: false,
      isFilterExpanded: false,
      isLoading: false,
      hasMore: true,
      page: 1,
      pageSize: 20,
      tasks: [],
      filterOptions: {
        type: []
      },
      // 当前位置信息（用于发送给后台）
      currentLocation: {
        latitude: null,
        longitude: null
      },
      filterTabs: [
        { label: '全部', value: 'all', count: 0 },
        { label: '待接单', value: 'new', count: 0 },
        { label: '进行中', value: 'accepted', count: 0 },
        { label: '已完成', value: 'completed', count: 0 }
      ],
      // 距离排序选项
      distanceSortOptions: [
        { label: '默认排序', value: 'default' },
        { label: '由近到远', value: 'distance_asc' },
        { label: '由远到近', value: 'distance_desc' }
      ],
      taskTypes: [
        { label: '安装', value: 'install' },
        { label: '维修', value: 'repair' },
        { label: '整改', value: 'rectification' }
      ]
    }
  },
  
  computed: {
    filteredTasks() {
      let result = this.tasks

      // 状态筛选
      if (this.currentFilter !== 'all') {
        result = result.filter(task => task.status === this.currentFilter)
      }

      // 搜索筛选
      if (this.searchKeyword) {
        const keyword = this.searchKeyword.toLowerCase()
        result = result.filter(task =>
          task.taskNumber.toLowerCase().includes(keyword) ||
          task.customerName.toLowerCase().includes(keyword) ||
          task.address.toLowerCase().includes(keyword) ||
          task.title.toLowerCase().includes(keyword)
        )
      }

      // 高级筛选
      if (this.filterOptions.type.length > 0) {
        result = result.filter(task => this.filterOptions.type.includes(task.type))
      }

      return result
    }
  },
  
  onLoad() {
    this.getCurrentLocation()
    this.loadTasks()
  },
  
  onPullDownRefresh() {
    this.refreshTasks()
  },
  
  onReachBottom() {
    this.loadMoreTasks()
  },
  
  methods: {
    // 加载任务数据
    async loadTasks() {
      this.isLoading = true
      try {
        // 模拟API调用
        await this.delay(1000)
        this.tasks = this.getMockTasks()
        this.updateFilterCounts()
      } catch (error) {
        console.error('加载任务失败:', error)
        this.showToast('加载失败，请重试')
      } finally {
        this.isLoading = false
      }
    },
    
    // 刷新任务
    async refreshTasks() {
      this.page = 1
      this.hasMore = true
      await this.loadTasks()
      uni.stopPullDownRefresh()
    },
    
    // 加载更多
    async loadMoreTasks() {
      if (!this.hasMore || this.isLoading) return

      this.page++
      this.isLoading = true

      try {
        await this.delay(1000)
        const newTasks = this.getMockTasks(this.page)
        this.tasks = [...this.tasks, ...newTasks]

        if (newTasks.length < this.pageSize) {
          this.hasMore = false
        }
      } catch (error) {
        console.error('加载更多失败:', error)
      } finally {
        this.isLoading = false
      }
    },

    // 搜索输入
    onSearchInput() {
      // 防抖处理
      clearTimeout(this.searchTimer)
      this.searchTimer = setTimeout(() => {
        this.updateFilterCounts()
      }, 300)
    },

    // 清除搜索
    clearSearch() {
      this.searchKeyword = ''
      this.updateFilterCounts()
    },

    // 切换筛选展开状态
    toggleFilterExpand() {
      this.isFilterExpanded = !this.isFilterExpanded
    },

    // 选择筛选项
    selectFilter(value) {
      this.currentFilter = value
      this.isFilterExpanded = false
    },

    // 获取当前筛选文本
    getCurrentFilterText() {
      const current = this.filterTabs.find(tab => tab.value === this.currentFilter)
      return current ? current.label : '全部'
    },

    // 获取当前筛选数量
    getCurrentFilterCount() {
      const current = this.filterTabs.find(tab => tab.value === this.currentFilter)
      return current ? current.count : 0
    },

    // 关闭筛选下拉菜单
    closeFilterDropdown() {
      this.isFilterExpanded = false
    },

    // 选择距离排序
    selectDistanceSort(value) {
      this.currentDistanceSort = value
      // 重新加载数据，将位置信息和排序方式发送给后台
      this.loadTasksWithLocation()
    },

    // 关闭所有下拉菜单
    closeAllDropdowns() {
      this.isFilterExpanded = false
    },

    // 显示筛选弹窗
    showFilterModal() {
      this.showFilter = true
    },

    // 隐藏筛选弹窗
    hideFilterModal() {
      this.showFilter = false
    },

    // 切换筛选选项
    toggleFilterOption(type, value) {
      const options = this.filterOptions[type]
      const index = options.indexOf(value)

      if (index > -1) {
        options.splice(index, 1)
      } else {
        options.push(value)
      }
    },

    // 重置筛选
    resetFilter() {
      this.filterOptions = {
        type: []
      }
    },

    // 应用筛选
    applyFilter() {
      this.hideFilterModal()
      this.updateFilterCounts()
    },

    // 更新筛选计数
    updateFilterCounts() {
      this.filterTabs.forEach(tab => {
        if (tab.value === 'all') {
          tab.count = this.tasks.length
        } else if (tab.isSort) {
          // 距离排序选项显示总任务数
          tab.count = this.tasks.length
        } else {
          tab.count = this.tasks.filter(task => task.status === tab.value).length
        }
      })
    },

    // 跳转任务详情
    goToTaskDetail(task) {
      uni.navigateTo({
        url: `/pages/task/task-detail?id=${task.id}`
      })
    },

    // 接单任务
    async acceptTask(task) {
      try {
        await this.delay(500)
        task.status = 'accepted'
        this.showToast('任务接单成功')
        this.updateFilterCounts()
      } catch (error) {
        this.showToast('接单失败，请重试')
      }
    },

    // 开始任务
    async startTask(task) {
      try {
        await this.delay(500)
        task.status = 'progress'
        this.showToast('任务已开始')
        this.updateFilterCounts()
      } catch (error) {
        this.showToast('操作失败，请重试')
      }
    },

    // 拨打电话
    callCustomer(task) {
      uni.makePhoneCall({
        phoneNumber: task.customerPhone || '************'
      })
    },

    // 打开导航
    openNavigation(task) {
      uni.showActionSheet({
        itemList: ['使用系统地图导航', '复制地址'],
        success: (res) => {
          if (res.tapIndex === 0) {
            // 使用系统地图导航
            uni.openLocation({
              latitude: task.latitude || 39.908823,
              longitude: task.longitude || 116.397470,
              name: task.customerName,
              address: task.address,
              success: () => {
                console.log('打开地图成功')
              },
              fail: (err) => {
                console.error('打开地图失败:', err)
                uni.showToast({
                  title: '打开地图失败',
                  icon: 'none'
                })
              }
            })
          } else if (res.tapIndex === 1) {
            // 复制地址
            const fullAddress = task.detailAddress ? `${task.address} ${task.detailAddress}` : task.address
            uni.setClipboardData({
              data: fullAddress,
              success: () => {
                uni.showToast({
                  title: '地址已复制',
                  icon: 'success'
                })
              }
            })
          }
        }
      })
    },

    // 获取状态文本
    getStatusText(status) {
      const statusMap = {
        'new': '待接单',
        'accepted': '已接单',
        'progress': '进行中',
        'completed': '已完成',
        'cancelled': '已取消'
      }
      return statusMap[status] || '未知'
    },

    // 获取任务类型文本
    getTypeText(type) {
      const typeMap = {
        'install': '安装',
        'repair': '维修',
        'rectification': '整改'
      }
      return typeMap[type] || '未知'
    },

    // 格式化时间 - 相对时间显示
    formatTime(time) {
      if (!time) return ''
      const date = new Date(time)
      const now = new Date()
      const diff = now - date

      // 刚刚（1分钟内）
      if (diff < 60000) return '刚刚'

      // 几分钟前（1小时内）
      if (diff < 3600000) return Math.floor(diff / 60000) + '分钟前'

      // 几小时前（24小时内）
      if (diff < 86400000) return Math.floor(diff / 3600000) + '小时前'

      // 超过一天，显示月日
      const month = String(date.getMonth() + 1).padStart(2, '0')
      const day = String(date.getDate()).padStart(2, '0')

      // 如果是今年，只显示月日
      if (date.getFullYear() === now.getFullYear()) {
        return `${month}-${day}`
      }

      // 如果不是今年，显示年月日
      const year = date.getFullYear()
      return `${year}-${month}-${day}`
    },

    // 获取空状态描述
    getEmptyDesc() {
      const descMap = {
        'all': '暂时没有任务',
        'new': '暂无待接单任务',
        'accepted': '暂无已接单任务',
        'progress': '暂无进行中任务',
        'completed': '暂无已完成任务'
      }
      return descMap[this.currentFilter] || '暂无数据'
    },

    // 显示提示
    showToast(title) {
      uni.showToast({
        title,
        icon: 'none'
      })
    },

    // 延迟函数
    delay(ms) {
      return new Promise(resolve => setTimeout(resolve, ms))
    },

    // 获取当前位置
    getCurrentLocation() {
      uni.getLocation({
        type: 'gcj02',
        success: (res) => {
          this.currentLocation = {
            latitude: res.latitude,
            longitude: res.longitude
          }
          console.log('获取位置成功:', this.currentLocation)
        },
        fail: (err) => {
          console.log('获取位置失败:', err)
          this.showToast('无法获取位置信息')
        }
      })
    },

    // 带位置信息加载任务数据
    loadTasksWithLocation() {
      if (!this.currentLocation.latitude || !this.currentLocation.longitude) {
        console.log('位置信息不可用，使用普通加载')
        this.loadTasks()
        return
      }

      this.isLoading = true

      // 构建请求参数
      const params = {
        page: this.page,
        pageSize: this.pageSize,
        latitude: this.currentLocation.latitude,
        longitude: this.currentLocation.longitude,
        distanceSort: this.currentDistanceSort,
        status: this.currentFilter !== 'all' ? this.currentFilter : undefined,
        searchKeyword: this.searchKeyword || undefined,
        types: this.filterOptions.type.length > 0 ? this.filterOptions.type : undefined
      }

      console.log('发送位置信息给后台:', params)

      // 这里应该调用真实的API
      // uni.request({
      //   url: '/api/tasks/with-location',
      //   method: 'GET',
      //   data: params,
      //   success: (res) => {
      //     // 处理返回的数据
      //   }
      // })

      // 暂时使用模拟数据
      setTimeout(() => {
        const mockTasks = this.getMockTasks(this.page)
        if (this.page === 1) {
          this.tasks = mockTasks
        } else {
          this.tasks = [...this.tasks, ...mockTasks]
        }
        this.updateFilterCounts()
        this.isLoading = false
      }, 1000)
    },

    // 获取模拟数据
    getMockTasks(page = 1) {
      const mockTasks = []
      const statuses = ['new', 'accepted', 'progress', 'completed']
      const types = ['install', 'repair', 'rectification']

      for (let i = 0; i < this.pageSize; i++) {
        const id = (page - 1) * this.pageSize + i + 1

        mockTasks.push({
          id: id,
          taskNumber: `GAS${String(id).padStart(6, '0')}`,
          title: `燃气设备${this.getTypeText(types[i % types.length])}任务`,
          customerName: `客户${id}`,
          customerPhone: `138${String(Math.floor(Math.random() * 10000)).padStart(4, '0')}${String(Math.floor(Math.random() * 10000)).padStart(4, '0')}`,
          address: `北京市朝阳区某某街道${id}号`,
          detailAddress: `${id}号楼${Math.floor(Math.random() * 6) + 1}单元${Math.floor(Math.random() * 20) + 1}01室`,
          deviceModel: 'GAS-2024-A1',
          status: statuses[i % statuses.length],
          type: types[i % types.length],
          createTime: new Date(Date.now() - Math.random() * 7 * 24 * 60 * 60 * 1000).toISOString(),
          // 模拟距离信息（实际应该由后台返回）
          distance: Math.round((Math.random() * 10 + 0.1) * 100) / 100, // 0.1-10.1km
          distanceText: this.formatDistance ? this.formatDistance(Math.round((Math.random() * 10 + 0.1) * 100) / 100) : ''
        })
      }

      return mockTasks
    },

    // 格式化距离显示（用于模拟数据）
    formatDistance(distance) {
      if (distance < 1) {
        return Math.round(distance * 1000) + 'm'
      } else {
        return distance.toFixed(1) + 'km'
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.task-list-container {
  min-height: 100vh;
  background: #f5f7fa;
}

/* 搜索栏 */
.search-section {
  background: #fff;
  padding: 24rpx;
  border-bottom: 1rpx solid #eee;
}

.search-bar {
  display: flex;
  align-items: center;
  background: #f8f9fa;
  border-radius: 24rpx;
  padding: 0 24rpx;
  height: 72rpx;
}

/* 搜索图标样式通过uni-icons组件设置 */

.search-input {
  flex: 1;
  font-size: 28rpx;
  color: #333;
}

/* 清除按钮样式通过uni-icons组件设置 */

/* 筛选标签 */
.filter-section {
  background: #fff;
  position: relative;
  border-bottom: 1rpx solid #eee;
}

/* 筛选行布局 */
.filter-row {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24rpx;
  gap: 16rpx;
}

/* 状态筛选 */
.status-filter {
  min-width: 180rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16rpx 20rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
  transition: all 0.2s ease;

  &:active {
    background: #e9ecef;
  }
}



.current-filter-content {
  display: flex;
  align-items: center;
  flex: 1;
}

.current-filter-text {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

.current-filter-badge {
  background: #4c6ef5;
  color: #fff;
  font-size: 20rpx;
  padding: 4rpx 12rpx;
  border-radius: 16rpx;
  margin-left: 16rpx;
  min-width: 32rpx;
  text-align: center;
  margin-right: 20rpx;
}

.expand-icon {
  transition: all 0.3s ease;
  opacity: 0.8;

  &.expanded {
    transform: rotate(180deg);
    opacity: 1;
  }

  &:hover {
    opacity: 1;
  }
}

/* 所有图标样式通过uni-icons组件设置 */

/* 下拉筛选选项 */
.filter-dropdown {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: #fff;
  border-radius: 0 0 16rpx 16rpx;
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.1);
  z-index: 100;
  max-height: 0;
  overflow: hidden;
  transition: all 0.3s ease;

  &.show {
    max-height: 400rpx;
    border-top: 1rpx solid #f0f0f0;
  }
}

.filter-options {
  padding: 16rpx 0;
}

.filter-option {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx 24rpx;
  transition: all 0.2s ease;

  &:active {
    background: #f8f9fa;
  }

  &.active {
    background: #f0f4ff;

    .option-text {
      color: #4c6ef5;
      font-weight: 500;
    }

    .option-icon {
      color: #4c6ef5;
    }

    .option-badge {
      background: #4c6ef5;
      color: #fff;
    }
  }

  &.is-sort {
    border-left: 4rpx solid #e8f5e8;

    &.active {
      border-left-color: #51cf66;
      background: #f0fff4;

      .option-text {
        color: #51cf66;
      }

      .option-icon {
        color: #51cf66;
      }

      .option-badge {
        background: #51cf66;
      }
    }
  }
}

.option-content {
  display: flex;
  align-items: center;
}

.option-icon {
  font-size: 24rpx;
  margin-right: 12rpx;
  color: #999;
}

.option-text {
  font-size: 28rpx;
  color: #333;
}

.option-badge {
  background: #f0f0f0;
  color: #666;
  font-size: 20rpx;
  padding: 4rpx 12rpx;
  border-radius: 16rpx;
  min-width: 32rpx;
  text-align: center;
}



.filter-actions {
  flex-shrink: 0;
}

.filter-btn {
  width: 60rpx;
  height: 60rpx;
  background: linear-gradient(135deg, #4c6ef5 0%, #3b5bdb 100%);
  border-radius: 16rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 6rpx 16rpx rgba(76, 110, 245, 0.25);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;

  &:hover {
    transform: translateY(-2rpx);
    box-shadow: 0 8rpx 20rpx rgba(76, 110, 245, 0.35);
  }

  &:active {
    transform: translateY(0);
    box-shadow: 0 4rpx 12rpx rgba(76, 110, 245, 0.4);
  }
}

/* 筛选图标样式已通过uni-icons组件设置 */

/* 遮罩层 */
.filter-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 99;
}

/* 任务列表 */
.task-scroll {
  flex: 1;
  padding: 24rpx;
}

.task-list {
  display: flex;
  flex-direction: column;
  gap: 24rpx;
}

.task-card {
  background: #fff;
  border-radius: 16rpx;
  padding: 32rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
  border: 1rpx solid #f0f0f0;
  transition: all 0.2s ease;

  &:active {
    transform: scale(0.98);
    box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
  }
}

/* 任务头部 */
.task-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 20rpx;
}

.task-header-left {
  display: flex;
  flex-direction: column;
  gap: 12rpx;
}

.task-header-right {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 8rpx;
}

.task-badges {
  display: flex;
  gap: 8rpx;
}

.task-number {
  font-size: 28rpx;
  color: #4c6ef5;
  font-weight: 600;
}

.task-time {
  font-size: 24rpx;
  color: #999;
}

.task-status-badge {
  padding: 6rpx 12rpx;
  border-radius: 12rpx;
  font-size: 20rpx;

  &.status-new {
    background: #e3f2fd;
    color: #1976d2;
  }

  &.status-accepted {
    background: #f3e5f5;
    color: #7b1fa2;
  }

  &.status-progress {
    background: #fff3e0;
    color: #f57c00;
  }

  &.status-completed {
    background: #e8f5e8;
    color: #388e3c;
  }
}

.task-type-badge {
  padding: 6rpx 12rpx;
  border-radius: 12rpx;
  font-size: 20rpx;

  &.type-install {
    background: #e8f5e8;
    color: #388e3c;
  }

  &.type-repair {
    background: #fff3e0;
    color: #f57c00;
  }

  &.type-rectification {
    background: #ffebee;
    color: #d32f2f;
  }
}

.task-distance {
  font-size: 20rpx;
  color: #999;
  background: transparent;
  border: 1rpx solid #e0e0e0;
  padding: 4rpx 8rpx;
  border-radius: 8rpx;
  font-weight: normal;
}

/* 任务主要信息 */
.task-main {
  margin-bottom: 20rpx;
}

.task-title {
  font-size: 30rpx;
  color: #333;
  font-weight: 600;
  margin-bottom: 16rpx;
  display: block;
  line-height: 1.4;
}

.task-customer-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12rpx;
}

.customer-info {
  display: flex;
  align-items: center;
  gap: 8rpx;
}



.task-customer {
  font-size: 26rpx;
  color: #666;
}

.task-phone {
  display: flex;
  align-items: center;
  gap: 6rpx;
  font-size: 22rpx;
  color: #4c6ef5;
  background: #f0f4ff;
  padding: 6rpx 10rpx;
  border-radius: 12rpx;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;

  &:active {
    background: #e6f0ff;
    transform: scale(0.95);
  }
}

.task-address {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12rpx 16rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
  cursor: pointer;
  transition: all 0.2s ease;

  &:active {
    background: #e9ecef;
  }
}

.address-content {
  display: flex;
  align-items: flex-start;
  flex: 1;
  gap: 8rpx;
}

.address-info {
  display: flex;
  flex-direction: column;
  flex: 1;
  gap: 4rpx;
}

.address-main {
  font-size: 26rpx;
  color: #666;
  line-height: 1.3;
}

.address-detail {
  font-size: 24rpx;
  color: #999;
  line-height: 1.2;
}

/* 任务操作 - 底部右边 */
.task-actions {
  display: flex;
  justify-content: flex-end;
  margin-top: 16rpx;
  padding-top: 16rpx;
  border-top: 1rpx solid #f0f0f0;
}

.action-btn {
  border-radius: 16rpx;
  font-size: 24rpx;
  border: none;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.1);
  height: 60rpx;
  width: 200rpx;
  padding: 0 16rpx;
  font-weight: 500;
  margin: inherit;

  &.primary {
    background: linear-gradient(135deg, #4c6ef5 0%, #3b5bdb 100%);
    color: #fff;

    &:active {
      transform: translateY(1rpx);
      box-shadow: 0 1rpx 4rpx rgba(76, 110, 245, 0.3);
    }
  }

  &.success {
    background: linear-gradient(135deg, #51cf66 0%, #40c057 100%);
    color: #fff;

    &:active {
      transform: translateY(1rpx);
      box-shadow: 0 1rpx 4rpx rgba(81, 207, 102, 0.3);
    }
  }
}

/* 状态显示 */
.loading-state,
.empty-state,
.no-more {
  text-align: center;
  padding: 80rpx 40rpx;
}

.loading-text,
.empty-text,
.no-more-text {
  color: #999;
  font-size: 28rpx;
}

.empty-icon {
  display: block;
  margin-bottom: 24rpx;
}

.empty-desc {
  color: #ccc;
  font-size: 24rpx;
  margin-top: 16rpx;
  display: block;
}

/* 筛选弹窗 */
.filter-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: flex-end;
  z-index: 1000;
}

.filter-content {
  background: #fff;
  border-radius: 24rpx 24rpx 0 0;
  width: 100%;
  max-height: 80vh;
  overflow: hidden;
}

.filter-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 32rpx;
  border-bottom: 1rpx solid #eee;
}

.filter-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.filter-close {
  padding: 8rpx;
  cursor: pointer;
}

.filter-body {
  padding: 32rpx;
  max-height: 60vh;
  overflow-y: auto;
}

.filter-group {
  margin-bottom: 48rpx;
}

.group-title {
  font-size: 28rpx;
  color: #333;
  font-weight: 600;
  margin-bottom: 24rpx;
  display: block;
}

.option-list {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
}

.option-item {
  padding: 16rpx 24rpx;
  background: #f8f9fa;
  border-radius: 24rpx;
  border: 2rpx solid transparent;

  &.active {
    background: #4c6ef5;
    color: #fff;

    .option-text {
      color: #fff;
    }
  }
}

.option-text {
  font-size: 26rpx;
  color: #666;
}

.filter-footer {
  display: flex;
  padding: 32rpx;
  gap: 24rpx;
  border-top: 1rpx solid #eee;
}

.filter-reset,
.filter-confirm {
  flex: 1;
  height: 88rpx;
  border-radius: 24rpx;
  font-size: 32rpx;
  border: none;
}

.filter-reset {
  background: #f8f9fa;
  color: #666;
  border: none;
}
.filter-reset::after {
  border: none;
}

.filter-confirm {
  background: #4c6ef5;
  color: #fff;
}
</style>
