<template>
  <view class="page-container">
    <!-- 设备列表 -->
    <scroll-view class="content-scroll" scroll-y="true">
      <view class="device-list">
        <view 
          class="device-card" 
          v-for="alarm in alarms" 
          :key="alarm.id"
          @click="showDeviceDetail(alarm)"
        >
          <!-- 设备头部 -->
          <view class="device-header">
            <view class="device-info">
              <view class="device-code-row">
                <view class="alarm-type-icon" :class="alarm.type">
                  <uni-icons :type="getAlarmIcon(alarm.type)" size="16" color="#fff"></uni-icons>
                </view>
                <text class="device-code">{{ alarm.code }}</text>
                <view class="status-badge" :class="alarm.status">
                  <text class="status-text">{{ getStatusText(alarm.status) }}</text>
                </view>
              </view>
              <text class="device-type">{{ getAlarmTypeText(alarm.type) }}</text>
            </view>
          </view>
          
          <!-- 设备详情 -->
          <view class="device-details">
            <view class="detail-grid">
              <view class="detail-item">
                <text class="detail-label">设备类型</text>
                <text class="detail-value">{{ getAlarmTypeText(alarm.type) }}</text>
              </view>
              <view class="detail-item">
                <text class="detail-label">安装时间</text>
                <text class="detail-value">{{ alarm.installTime }}</text>
              </view>
              <view class="detail-item">
                <text class="detail-label">设备状态</text>
                <text class="detail-value" :class="'status-' + alarm.status">{{ getStatusText(alarm.status) }}</text>
              </view>
              <view class="detail-item">
                <text class="detail-label">最后检测</text>
                <text class="detail-value">2小时前</text>
              </view>
            </view>
            
            <!-- 电量显示 -->
            <view class="battery-section">
              <view class="battery-info">
                <uni-icons type="battery" size="16" color="#52c41a"></uni-icons>
                <text class="battery-label">设备电量</text>
                <text class="battery-value" :class="getBatteryClass(alarm.batteryLevel)">{{ alarm.batteryLevel }}%</text>
              </view>
              <view class="battery-bar">
                <view 
                  class="battery-fill" 
                  :class="getBatteryClass(alarm.batteryLevel)"
                  :style="{ width: alarm.batteryLevel + '%' }"
                ></view>
              </view>
            </view>
          </view>
        </view>
      </view>
      
      <!-- 底部安全区域 -->
      <view class="safe-bottom"></view>
    </scroll-view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      taskId: '',
      alarms: [
        {
          id: 1,
          type: 'home', // home/commercial/co
          code: 'AL001234567',
          batteryLevel: 85,
          status: 'normal',
          installTime: '2023-06-15'
        },
        {
          id: 2,
          type: 'commercial',
          code: 'AL001234568',
          batteryLevel: 45,
          status: 'low_battery',
          installTime: '2023-05-20'
        },
        {
          id: 3,
          type: 'co',
          code: 'AL001234569',
          batteryLevel: 78,
          status: 'normal',
          installTime: '2023-07-01'
        },
        {
          id: 4,
          type: 'home',
          code: 'AL001234570',
          batteryLevel: 12,
          status: 'offline',
          installTime: '2023-04-10'
        }
      ]
    }
  },
  
  onLoad(options) {
    if (options.taskId) {
      this.taskId = options.taskId
    }
  },
  
  methods: {
    // 获取报警器类型文本
    getAlarmTypeText(type) {
      const typeMap = {
        'home': '家用燃气报警器',
        'commercial': '商用报警器',
        'co': '一氧化碳报警器'
      }
      return typeMap[type] || '未知类型'
    },
    
    // 获取报警器图标
    getAlarmIcon(type) {
      const iconMap = {
        'home': 'home-filled',
        'commercial': 'shop-filled',
        'co': 'sound-filled'
      }
      return iconMap[type] || 'sound'
    },
    
    // 获取状态文本
    getStatusText(status) {
      const statusMap = {
        'normal': '正常',
        'abnormal': '异常',
        'low_battery': '低电量',
        'offline': '离线'
      }
      return statusMap[status] || '未知'
    },
    
    // 获取电量样式类
    getBatteryClass(level) {
      if (level >= 60) return 'high'
      if (level >= 30) return 'medium'
      return 'low'
    },
    
    // 显示设备详情
    showDeviceDetail(alarm) {
      // 可以跳转到设备详情页面或显示弹窗
      console.log('显示设备详情:', alarm)
    }
  }
}
</script>

<style lang="scss" scoped>
.page-container {
  min-height: 100vh;
  width: 100vw;
  max-width: 100vw;
  background: #f8f9fa;
  display: flex;
  flex-direction: column;
  overflow-x: hidden;
}

// 内容滚动区域
.content-scroll {
  flex: 1;
  width: 100%;
  max-width: 100%;
  padding: 24rpx 32rpx;
  box-sizing: border-box;
  overflow-x: hidden;
}

.device-list {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.device-card {
  width: 100%;
  max-width: 100%;
  background: #fff;
  border-radius: 24rpx;
  padding: 32rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
  box-sizing: border-box;
  overflow: hidden;
}

.device-header {
  margin-bottom: 24rpx;
}

.device-info {
  width: 100%;
}

.device-code-row {
  display: flex;
  align-items: center;
  gap: 12rpx;
  margin-bottom: 8rpx;
}

.alarm-type-icon {
  width: 32rpx;
  height: 32rpx;
  border-radius: 8rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  
  &.home {
    background: linear-gradient(135deg, #52c41a, #73d13d);
  }
  
  &.commercial {
    background: linear-gradient(135deg, #1890ff, #40a9ff);
  }
  
  &.co {
    background: linear-gradient(135deg, #ff6b6b, #ff8a8a);
  }
}

.device-code {
  font-size: 30rpx;
  font-weight: 600;
  color: #262626;
}

.status-badge {
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
  font-size: 22rpx;
  
  &.normal {
    background: rgba(82, 196, 26, 0.1);
    color: #52c41a;
  }
  
  &.abnormal {
    background: rgba(255, 77, 79, 0.1);
    color: #ff4d4f;
  }
  
  &.low_battery {
    background: rgba(250, 173, 20, 0.1);
    color: #faad14;
  }
  
  &.offline {
    background: rgba(140, 140, 140, 0.1);
    color: #8c8c8c;
  }
}

.device-type {
  font-size: 24rpx;
  color: #8c8c8c;
}

.device-details {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.detail-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16rpx;
  width: 100%;
  max-width: 100%;
  box-sizing: border-box;
}

.detail-item {
  display: flex;
  flex-direction: column;
  gap: 4rpx;
  padding: 16rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
  width: 100%;
  max-width: 100%;
  box-sizing: border-box;
  overflow: hidden;
}

.detail-label {
  font-size: 22rpx;
  color: #8c8c8c;
}

.detail-value {
  font-size: 26rpx;
  color: #262626;
  font-weight: 500;
  
  &.status-normal {
    color: #52c41a;
  }
  
  &.status-abnormal {
    color: #ff4d4f;
  }
  
  &.status-low_battery {
    color: #faad14;
  }
  
  &.status-offline {
    color: #8c8c8c;
  }
}

.battery-section {
  padding: 20rpx;
  background: #f8f9fa;
  border-radius: 16rpx;
}

.battery-info {
  display: flex;
  align-items: center;
  gap: 8rpx;
  margin-bottom: 12rpx;
}

.battery-label {
  font-size: 26rpx;
  color: #262626;
  font-weight: 500;
}

.battery-value {
  font-size: 26rpx;
  font-weight: 600;
  margin-left: auto;
  
  &.high {
    color: #52c41a;
  }
  
  &.medium {
    color: #faad14;
  }
  
  &.low {
    color: #ff6b6b;
  }
}

.battery-bar {
  height: 8rpx;
  background: #e8e8e8;
  border-radius: 4rpx;
  overflow: hidden;
}

.battery-fill {
  height: 100%;
  border-radius: 4rpx;
  transition: all 0.3s ease;
  
  &.high {
    background: linear-gradient(90deg, #52c41a, #73d13d);
  }
  
  &.medium {
    background: linear-gradient(90deg, #faad14, #ffc53d);
  }
  
  &.low {
    background: linear-gradient(90deg, #ff6b6b, #ff8a8a);
  }
}

.safe-bottom {
  height: 32rpx;
}
</style>
