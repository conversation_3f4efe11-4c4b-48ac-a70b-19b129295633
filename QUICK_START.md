# 燃气管理系统 - 快速启动指南

## 🚀 快速开始

### 1. 环境准备

确保您的开发环境已安装：
- **HBuilderX** 3.0+ (推荐最新版本)
- **Node.js** 14+ (用于某些插件和工具)
- **微信开发者工具** (如需开发小程序)

### 2. 项目导入

1. 打开 HBuilderX
2. 点击 `文件` -> `导入` -> `从本地目录导入`
3. 选择项目根目录 `gas-mgmt-system`
4. 等待项目加载完成

### 3. 运行项目

#### H5 平台运行
1. 在 HBuilderX 中右键点击项目根目录
2. 选择 `运行` -> `运行到浏览器` -> `Chrome`
3. 项目将自动编译并在浏览器中打开

#### 微信小程序运行
1. 确保已安装微信开发者工具
2. 在 HBuilderX 中右键点击项目根目录
3. 选择 `运行` -> `运行到小程序模拟器` -> `微信开发者工具`
4. 首次运行需要配置微信开发者工具路径

### 4. 项目配置

#### 修改应用信息
编辑 `manifest.json` 文件：
```json
{
  "name": "燃气管理系统",
  "appid": "__UNI__XXXXXXX",
  "description": "现代化燃气设备管理平台",
  "versionName": "1.0.0"
}
```

#### 配置API地址
编辑 `src/api/index.js` 文件：
```javascript
const BASE_URL = 'https://your-api-domain.com'
```

#### 微信小程序配置
如需发布微信小程序，需要：
1. 在微信公众平台注册小程序
2. 获取 AppID
3. 在 `manifest.json` 中配置 AppID

### 5. 功能测试

#### 登录功能测试
1. 打开应用后进入登录页面
2. 输入手机号：`13800138000`
3. 点击获取验证码（开发环境下验证码为：`123456`）
4. 选择角色（维保员/巡检员/配送员）
5. 点击登录

#### 主要功能页面
- **工作台首页**: 查看数据概览和快速操作
- **任务管理**: 查看和管理工作任务
- **现场执行**: 现场作业工具和流程
- **个人中心**: 个人信息和工作统计
- **知识库**: 技术文档和操作指南

### 6. 开发调试

#### 开启调试模式
在 `src/main.js` 中：
```javascript
Vue.config.productionTip = false
Vue.config.devtools = true
```

#### 查看控制台
- **H5**: 浏览器开发者工具 Console
- **小程序**: 微信开发者工具 Console

#### 网络请求调试
在 `src/api/index.js` 中已配置请求拦截器，会自动打印请求和响应信息。

### 7. 常见问题

#### Q: SCSS 变量未定义错误
A: 确保 `src/uni.scss` 文件中包含所有必要的变量定义。

#### Q: 页面跳转失败
A: 检查 `src/pages.json` 中的页面路径配置是否正确。

#### Q: API 请求失败
A: 检查网络连接和 API 地址配置，确保后端服务正常运行。

#### Q: 小程序真机调试问题
A: 确保手机和电脑在同一网络，并开启调试模式。

### 8. 构建发布

#### H5 发布
1. 右键项目 -> `发行` -> `网站-H5手机版`
2. 配置发行参数
3. 点击发行，生成 `unpackage/dist/build/h5` 目录
4. 将生成的文件部署到 Web 服务器

#### 小程序发布
1. 右键项目 -> `发行` -> `小程序-微信`
2. 配置小程序信息
3. 点击发行，生成 `unpackage/dist/build/mp-weixin` 目录
4. 使用微信开发者工具打开生成的目录
5. 上传代码到微信平台审核

### 9. 技术支持

如遇到问题，请检查：
1. HBuilderX 版本是否为最新
2. 项目依赖是否完整
3. 网络连接是否正常
4. 相关开发工具是否正确安装

### 10. 下一步开发

建议的开发顺序：
1. 完善 API 接口对接
2. 添加更多业务功能
3. 优化用户体验
4. 添加单元测试
5. 性能优化

---

🎉 **恭喜！您已成功启动燃气管理系统！**

如有任何问题，请参考项目文档或联系技术支持。
