<template>
  <view class="page-container">
    <!-- 顶部状态栏 -->
    <view class="header-section">
      <view class="task-header">
        <view class="task-info">
          <text class="task-number">{{ taskDetail.taskNumber }}</text>
          <text class="task-title">{{ taskDetail.title }}</text>
        </view>
        <view class="task-badges">
          <view class="status-badge" :class="'status-' + taskDetail.status">
            <uni-icons type="flag-filled" size="12" color="#fff"></uni-icons>
            <text class="badge-text">{{ getStatusText(taskDetail.status) }}</text>
          </view>
          <view class="type-badge" :class="'type-' + taskDetail.type">
            <uni-icons type="gear-filled" size="12" color="#fff"></uni-icons>
            <text class="badge-text">{{ getTaskTypeText(taskDetail.type) }}</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 主要内容 -->
    <scroll-view class="content-scroll" scroll-y="true">
      <!-- 客户信息卡片 -->
      <view class="section-card">
        <view class="card-header">
          <view class="header-left">
            <view class="icon-wrapper customer-icon">
              <uni-icons type="contact-filled" size="18" color="#fff"></uni-icons>
            </view>
            <text class="section-title">客户信息</text>
          </view>
          <view class="header-actions">
            <view class="action-btn phone-btn" @click="callCustomer">
              <uni-icons type="phone-filled" size="16" color="#fff"></uni-icons>
            </view>
            <view class="action-btn nav-btn" @click="openNavigation">
              <uni-icons type="location-filled" size="16" color="#fff"></uni-icons>
            </view>
          </view>
        </view>

        <view class="card-body">
          <view class="customer-main">
            <view class="customer-name">
              <uni-icons type="person-filled" size="16" color="#4c6ef5"></uni-icons>
              <text class="name-text">{{ taskDetail.customerName }}</text>
            </view>
            <view class="customer-phone" @click="callCustomer">
              <uni-icons type="phone" size="16" color="#52c41a"></uni-icons>
              <text class="phone-text">{{ taskDetail.customerPhone }}</text>
            </view>
          </view>

          <view class="address-section">
            <view class="address-item" @click="openNavigation">
              <view class="address-icon">
                <uni-icons type="location" size="16" color="#ff6b6b"></uni-icons>
              </view>
              <view class="address-content">
                <text class="address-main">{{ taskDetail.address }}</text>
                <text v-if="taskDetail.detailAddress" class="address-detail">{{ taskDetail.detailAddress }}</text>
              </view>
              <uni-icons type="right" size="14" color="#ccc"></uni-icons>
            </view>
          </view>
        </view>
      </view>

      <!-- 客户设备卡片 -->
      <view class="section-card">
        <view class="card-header">
          <view class="header-left">
            <view class="icon-wrapper equipment-icon">
              <uni-icons type="gear-filled" size="18" color="#fff"></uni-icons>
            </view>
            <text class="section-title">客户设备</text>
          </view>
        </view>

        <view class="card-body">
          <view class="equipment-summary">
            <!-- 气瓶设备统计 -->
            <view class="equipment-item" @click="showGasCylinders">
              <view class="equipment-icon-wrapper gas-cylinder">
                <uni-icons type="fire" size="20" color="#fff"></uni-icons>
              </view>
              <view class="equipment-info">
                <text class="equipment-name">气瓶设备</text>
                <text class="equipment-count">{{ gasCylinders.length }}台</text>
              </view>
              <view class="equipment-status">
                <text class="normal-count">{{ normalGasCylinders }}台正常</text>
                <text class="abnormal-count" v-if="abnormalGasCylinders > 0">{{ abnormalGasCylinders }}台异常</text>
              </view>
              <uni-icons type="right" size="16" color="#ccc"></uni-icons>
            </view>

            <!-- 报警器设备统计 -->
            <view class="equipment-item" @click="showAlarms">
              <view class="equipment-icon-wrapper alarm">
                <uni-icons type="sound" size="20" color="#fff"></uni-icons>
              </view>
              <view class="equipment-info">
                <text class="equipment-name">报警器设备</text>
                <text class="equipment-count">{{ alarms.length }}台</text>
              </view>
              <view class="equipment-status">
                <text class="normal-count">{{ normalAlarms }}台正常</text>
                <text class="abnormal-count" v-if="abnormalAlarms > 0">{{ abnormalAlarms }}台异常</text>
              </view>
              <uni-icons type="right" size="16" color="#ccc"></uni-icons>
            </view>
          </view>
        </view>
      </view>

      <!-- 服务信息卡片 -->
      <view class="section-card">
        <view class="card-header">
          <view class="header-left">
            <view class="icon-wrapper service-icon">
              <uni-icons type="wrench-filled" size="18" color="#fff"></uni-icons>
            </view>
            <text class="section-title">服务内容</text>
          </view>
        </view>

        <view class="card-body">
          <view class="service-info">
            <view class="service-row">
              <view class="service-item">
                <view class="service-icon-wrapper">
                  <uni-icons type="flag" size="14" color="#faad14"></uni-icons>
                </view>
                <view class="service-content">
                  <text class="service-label">任务类型</text>
                  <text class="service-value">{{ getTaskTypeText(taskDetail.type) }}</text>
                </view>
              </view>

              <view class="service-item">
                <view class="service-icon-wrapper">
                  <uni-icons type="calendar" size="14" color="#1890ff"></uni-icons>
                </view>
                <view class="service-content">
                  <text class="service-label">预约时间</text>
                  <text class="service-value">{{ formatDateTime(taskDetail.appointmentTime) }}</text>
                </view>
              </view>
            </view>

            <view class="requirements-section">
              <view class="requirements-header">
                <uni-icons type="compose" size="14" color="#52c41a"></uni-icons>
                <text class="requirements-title">服务要求</text>
              </view>
              <text class="requirements-content">{{ taskDetail.requirements }}</text>
            </view>

            <view v-if="taskDetail.remarks" class="remarks-section">
              <view class="remarks-header">
                <uni-icons type="chatbubble" size="14" color="#ff6b6b"></uni-icons>
                <text class="remarks-title">备注信息</text>
              </view>
              <text class="remarks-content">{{ taskDetail.remarks }}</text>
            </view>
          </view>
        </view>
      </view>
      
      <!-- 使用耗材卡片 -->
      <view class="section-card">
        <view class="card-header">
          <view class="header-left">
            <view class="icon-wrapper parts-icon">
              <uni-icons type="box" size="18" color="#fff"></uni-icons>
            </view>
            <text class="section-title">使用耗材</text>
          </view>
          <view class="action-btn scan-btn" @click="scanParts">
            <uni-icons type="scan" size="16" color="#fff"></uni-icons>
          </view>
        </view>

        <view class="card-body">
          <view v-if="taskDetail.parts && taskDetail.parts.length > 0" class="parts-container">
            <view
              class="part-card"
              v-for="part in taskDetail.parts"
              :key="part.id"
            >
              <view class="part-header">
                <view class="part-icon">
                  <uni-icons type="cube" size="16" color="#722ed1"></uni-icons>
                </view>
                <view class="part-main">
                  <text class="part-name">{{ part.name }}</text>
                  <text class="part-spec">{{ part.specification }}</text>
                </view>
                <view class="part-quantity">
                  <text class="quantity-number">{{ part.quantity }}</text>
                  <text class="quantity-unit">{{ part.unit }}</text>
                </view>
              </view>
            </view>
          </view>
          <view v-else class="empty-state">
            <view class="empty-icon">
              <uni-icons type="inbox" size="32" color="#d9d9d9"></uni-icons>
            </view>
            <text class="empty-text">暂无使用耗材要求</text>
          </view>
        </view>
      </view>
      
      <!-- 操作流程卡片 -->
      <view class="section-card">
        <view class="card-header">
          <view class="header-left">
            <view class="icon-wrapper process-icon">
              <uni-icons type="list-filled" size="18" color="#fff"></uni-icons>
            </view>
            <text class="section-title">操作流程</text>
          </view>
        </view>

        <view class="card-body">
          <view class="process-timeline">
            <view
              class="timeline-item"
              :class="{
                'is-active': step.status === 'active',
                'is-completed': step.status === 'completed',
                'is-last': index === processSteps.length - 1
              }"
              v-for="(step, index) in processSteps"
              :key="index"
            >
              <view class="timeline-node">
                <view class="node-icon">
                  <uni-icons
                    v-if="step.status === 'completed'"
                    type="checkmarkempty"
                    size="14"
                    color="#fff"
                  ></uni-icons>
                  <text v-else class="node-number">{{ index + 1 }}</text>
                </view>
              </view>

              <view class="timeline-content">
                <view class="step-header">
                  <text class="step-title">{{ step.title }}</text>
                  <text v-if="step.time" class="step-time">
                    <uni-icons type="time" size="12" color="#999"></uni-icons>
                    {{ formatDateTime(step.time) }}
                  </text>
                </view>
                <text class="step-desc">{{ step.description }}</text>
              </view>
            </view>
          </view>
        </view>
      </view>
      
      <!-- 客户设备卡片 -->
      <view class="section-card">
        <view class="card-header">
          <view class="header-left">
            <view class="icon-wrapper equipment-icon">
              <uni-icons type="gear-filled" size="18" color="#fff"></uni-icons>
            </view>
            <text class="section-title">客户设备</text>
          </view>
        </view>

        <view class="card-body">
          <view class="equipment-summary">
            <!-- 气瓶设备统计 -->
            <view class="equipment-item" @click="showGasCylinders">
              <view class="equipment-icon-wrapper gas-cylinder">
                <uni-icons type="fire" size="20" color="#fff"></uni-icons>
              </view>
              <view class="equipment-info">
                <text class="equipment-name">气瓶设备</text>
                <text class="equipment-count">{{ gasCylinders.length }}台</text>
              </view>
              <view class="equipment-status">
                <text class="normal-count">{{ normalGasCylinders }}台正常</text>
                <text class="abnormal-count" v-if="abnormalGasCylinders > 0">{{ abnormalGasCylinders }}台异常</text>
              </view>
              <uni-icons type="right" size="16" color="#ccc"></uni-icons>
            </view>

            <!-- 报警器设备统计 -->
            <view class="equipment-item" @click="showAlarms">
              <view class="equipment-icon-wrapper alarm">
                <uni-icons type="sound" size="20" color="#fff"></uni-icons>
              </view>
              <view class="equipment-info">
                <text class="equipment-name">报警器设备</text>
                <text class="equipment-count">{{ alarms.length }}台</text>
              </view>
              <view class="equipment-status">
                <text class="normal-count">{{ normalAlarms }}台正常</text>
                <text class="abnormal-count" v-if="abnormalAlarms > 0">{{ abnormalAlarms }}台异常</text>
              </view>
              <uni-icons type="right" size="16" color="#ccc"></uni-icons>
            </view>
          </view>
        </view>
      </view>

      <!-- 工作记录卡片 -->
      <view class="section-card">
        <view class="card-header">
          <view class="header-left">
            <view class="icon-wrapper record-icon">
              <uni-icons type="compose" size="18" color="#fff"></uni-icons>
            </view>
            <text class="section-title">工作记录</text>
          </view>
          <view class="action-btn add-btn" @click="addRecord">
            <uni-icons type="plus" size="16" color="#fff"></uni-icons>
          </view>
        </view>

        <view class="card-body">
          <view v-if="workRecords.length > 0" class="records-container">
            <view
              class="record-card"
              v-for="record in workRecords"
              :key="record.id"
            >
              <view class="record-header">
                <view class="record-type-badge">
                  <uni-icons type="flag-filled" size="12" color="#eb2f96"></uni-icons>
                  <text class="record-type">{{ record.type }}</text>
                </view>
                <view class="record-time">
                  <uni-icons type="time" size="12" color="#999"></uni-icons>
                  <text class="time-text">{{ formatDateTime(record.time) }}</text>
                </view>
              </view>

              <text class="record-content">{{ record.content }}</text>

              <view v-if="record.images && record.images.length > 0" class="record-images">
                <view
                  class="image-item"
                  v-for="(image, index) in record.images"
                  :key="index"
                  @click="previewImage(record.images, index)"
                >
                  <image
                    class="record-image"
                    :src="image"
                    mode="aspectFill"
                  />
                  <view class="image-mask">
                    <uni-icons type="eye" size="14" color="#fff"></uni-icons>
                  </view>
                </view>
              </view>
            </view>
          </view>

          <view v-else class="empty-state">
            <view class="empty-icon">
              <uni-icons type="compose" size="32" color="#d9d9d9"></uni-icons>
            </view>
            <text class="empty-text">暂无工作记录</text>
          </view>
        </view>
      </view>

      <!-- 底部安全区域 -->
      <view class="safe-bottom"></view>
    </scroll-view>

    <!-- 底部操作栏 -->
    <view class="bottom-bar">
      <view class="action-buttons">
        <button
          v-if="taskDetail.status === 'new'"
          class="main-button accept-btn"
          @click="acceptTask"
        >
          <uni-icons type="checkmarkempty" size="16" color="#fff"></uni-icons>
          <text class="button-text">接单任务</text>
        </button>

        <button
          v-if="taskDetail.status === 'accepted'"
          class="main-button start-btn"
          @click="startTask"
        >
          <uni-icons type="play-filled" size="16" color="#fff"></uni-icons>
          <text class="button-text">开始作业</text>
        </button>

        <template v-if="taskDetail.status === 'progress'">
          <button class="secondary-button" @click="reportProblem">
            <uni-icons type="info-filled" size="16" color="#ff6b6b"></uni-icons>
            <text class="button-text">上报问题</text>
          </button>
          <button class="main-button complete-btn" @click="completeTask">
            <uni-icons type="checkmarkempty" size="16" color="#fff"></uni-icons>
            <text class="button-text">完成作业</text>
          </button>
        </template>

        <button
          v-if="taskDetail.status === 'completed'"
          class="secondary-button"
          @click="viewReport"
        >
          <uni-icons type="eye" size="16" color="#1890ff"></uni-icons>
          <text class="button-text">查看报告</text>
        </button>
      </view>
    </view>
  </view>
</template>

<script>
import { taskApi } from '@/api/index.js'
import { formatDate, makePhoneCall, openMap, scanCode, previewImage, showToast, showConfirm } from '@/utils/index.js'

export default {
  data() {
    return {
      taskId: '',
      taskDetail: {},
      workRecords: [],
      processSteps: [
        { title: '任务接收', description: '确认接收任务', status: 'pending' },
        { title: '前往现场', description: '导航到客户地址', status: 'pending' },
        { title: '开始作业', description: '到达现场开始工作', status: 'pending' },
        { title: '完成作业', description: '完成所有工作内容', status: 'pending' },
        { title: '客户确认', description: '客户签字确认', status: 'pending' }
      ],
      // 气瓶设备数据
      gasCylinders: [
        {
          id: 1,
          code: 'LPG77778888',
          alarmCode: '未设置',
          fillingUnit: '充装单位F',
          fillingTime: '2023-08-10',
          fillingWeight: '11kg',
          alarmStatus: '正常',
          valveStatus: 'open', // open/closed
          batteryLevel: 92,
          status: 'normal' // normal/abnormal
        },
        {
          id: 2,
          code: 'LPG99990000',
          alarmCode: '未设置',
          fillingUnit: '充装单位G',
          fillingTime: '2023-08-10',
          fillingWeight: '10kg',
          alarmStatus: '正常',
          valveStatus: 'closed',
          batteryLevel: 67,
          status: 'normal'
        }
      ],
      // 报警器设备数据
      alarms: [
        {
          id: 1,
          type: 'home', // home/commercial/co
          code: 'AL001234567',
          batteryLevel: 85,
          status: 'normal',
          installTime: '2023-06-15'
        },
        {
          id: 2,
          type: 'commercial',
          code: 'AL001234568',
          batteryLevel: 45,
          status: 'low_battery',
          installTime: '2023-05-20'
        },
        {
          id: 3,
          type: 'co',
          code: 'AL001234569',
          batteryLevel: 78,
          status: 'normal',
          installTime: '2023-07-01'
        }
      ]
    }
  },
  
  onLoad(options) {
    this.taskId = options.id
    this.loadTaskDetail()
  },
  
  onShow() {
    // 页面显示时刷新数据
    if (this.taskId) {
      this.loadTaskDetail()
    }
  },

  computed: {
    // 正常气瓶数量
    normalGasCylinders() {
      return this.gasCylinders.filter(item => item.status === 'normal').length
    },

    // 异常气瓶数量
    abnormalGasCylinders() {
      return this.gasCylinders.filter(item => item.status !== 'normal').length
    },

    // 正常报警器数量
    normalAlarms() {
      return this.alarms.filter(item => item.status === 'normal').length
    },

    // 异常报警器数量
    abnormalAlarms() {
      return this.alarms.filter(item => item.status !== 'normal').length
    }
  },

  methods: {
    // 加载任务详情
    async loadTaskDetail() {
      try {
        const detail = await taskApi.getTaskDetail(this.taskId)
        this.taskDetail = detail
        this.workRecords = detail.workRecords || []
        this.updateProcessSteps()
      } catch (error) {
        showToast('加载失败')
        uni.navigateBack()
      }
    },
    
    // 更新流程步骤状态
    updateProcessSteps() {
      const status = this.taskDetail.status
      
      // 重置所有步骤
      this.processSteps.forEach(step => {
        step.status = 'pending'
        step.time = null
      })
      
      // 根据任务状态更新步骤
      if (status === 'accepted' || status === 'progress' || status === 'completed') {
        this.processSteps[0].status = 'completed'
        this.processSteps[0].time = this.taskDetail.acceptTime
      }
      
      if (status === 'progress' || status === 'completed') {
        this.processSteps[1].status = 'completed'
        this.processSteps[2].status = 'completed'
        this.processSteps[2].time = this.taskDetail.startTime
      }
      
      if (status === 'completed') {
        this.processSteps[3].status = 'completed'
        this.processSteps[4].status = 'completed'
        this.processSteps[3].time = this.taskDetail.completeTime
      }
      
      // 设置当前活动步骤
      if (status === 'new') {
        this.processSteps[0].status = 'active'
      } else if (status === 'accepted') {
        this.processSteps[1].status = 'active'
      } else if (status === 'progress') {
        this.processSteps[3].status = 'active'
      }
    },
    
    // 格式化日期时间
    formatDateTime(datetime) {
      return formatDate(datetime, 'YYYY-MM-DD HH:mm')
    },



    // 显示气瓶设备列表
    showGasCylinders() {
      uni.navigateTo({
        url: `/pages/equipment/gas-cylinders?taskId=${this.taskId}`
      })
    },

    // 显示报警器设备列表
    showAlarms() {
      uni.navigateTo({
        url: `/pages/equipment/alarms?taskId=${this.taskId}`
      })
    },
    
    // 获取状态文本
    getStatusText(status) {
      const statusMap = {
        new: '待接单',
        accepted: '已接单',
        progress: '进行中',
        completed: '已完成',
        cancelled: '已取消'
      }
      return statusMap[status] || status
    },

    // 获取任务类型文本
    getTaskTypeText(type) {
      const typeMap = {
        install: '安装',
        repair: '维修',
        rectification: '整改'
      }
      return typeMap[type] || type
    },
    
    // 拨打客户电话
    callCustomer() {
      makePhoneCall(this.taskDetail.customerPhone)
    },
    
    // 打开导航
    openNavigation() {
      openMap(this.taskDetail.latitude, this.taskDetail.longitude, this.taskDetail.address)
    },
    
    // 扫描设备
    async scanDevice() {
      try {
        const result = await scanCode()
        showToast('扫描结果: ' + result)
        // 处理设备扫描结果
      } catch (error) {
        showToast('扫描失败')
      }
    },
    
    // 扫描备件
    async scanParts() {
      try {
        const result = await scanCode()
        showToast('扫描结果: ' + result)
        // 处理备件扫描结果
      } catch (error) {
        showToast('扫描失败')
      }
    },
    
    // 添加工作记录
    addRecord() {
      uni.navigateTo({
        url: `/pages/task/add-record?taskId=${this.taskId}`
      })
    },
    
    // 预览图片
    previewImage(images, current) {
      previewImage(images, current)
    },
    
    // 接收任务
    async acceptTask() {
      const confirmed = await showConfirm('确认接收此任务？')
      if (!confirmed) return
      
      try {
        await taskApi.acceptTask(this.taskId)
        showToast('任务接单成功')
        this.loadTaskDetail()
      } catch (error) {
        showToast('接单失败')
      }
    },
    
    // 开始任务
    async startTask() {
      const confirmed = await showConfirm('确认开始作业？')
      if (!confirmed) return
      
      try {
        await taskApi.startTask(this.taskId)
        showToast('任务已开始')
        this.loadTaskDetail()
      } catch (error) {
        showToast('开始失败')
      }
    },
    
    // 上报问题
    reportProblem() {
      uni.navigateTo({
        url: `/pages/task/report-problem?taskId=${this.taskId}`
      })
    },
    
    // 完成任务
    completeTask() {
      uni.navigateTo({
        url: `/pages/task/complete-task?taskId=${this.taskId}`
      })
    },
    
    // 查看报告
    viewReport() {
      uni.navigateTo({
        url: `/pages/task/task-report?taskId=${this.taskId}`
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.page-container {
  min-height: 100vh;
  width: 100vw;
  max-width: 100vw;
  background: #f8f9fa;
  display: flex;
  flex-direction: column;
  overflow-x: hidden;
}

// 顶部区域
.header-section {
  background: linear-gradient(135deg, #4c6ef5 0%, #6c5ce7 100%);
  padding: 32rpx 32rpx 24rpx;
  position: relative;

  &::after {
    content: '';
    position: absolute;
    bottom: -20rpx;
    left: 0;
    right: 0;
    height: 20rpx;
    background: #f8f9fa;
    border-radius: 20rpx 20rpx 0 0;
  }
}

.task-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: 24rpx;
}

.task-info {
  flex: 1;
  min-width: 0;
}

.task-number {
  font-size: 32rpx;
  font-weight: 600;
  color: #fff;
  margin-bottom: 8rpx;
}

.task-title {
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.9);
  line-height: 1.4;
}

.task-badges {
  display: flex;
  flex-direction: column;
  gap: 12rpx;
  flex-shrink: 0;
}

.status-badge, .type-badge {
  display: flex;
  align-items: center;
  gap: 6rpx;
  padding: 8rpx 12rpx;
  border-radius: 20rpx;
  backdrop-filter: blur(10rpx);

  &.status-new { background: rgba(255, 107, 107, 0.9); }
  &.status-accepted { background: rgba(82, 196, 26, 0.9); }
  &.status-progress { background: rgba(24, 144, 255, 0.9); }
  &.status-completed { background: rgba(82, 196, 26, 0.9); }

  &.type-install { background: rgba(82, 196, 26, 0.9); }
  &.type-repair { background: rgba(250, 173, 20, 0.9); }
  &.type-rectification { background: rgba(255, 77, 79, 0.9); }
}

.badge-text {
  font-size: 22rpx;
  font-weight: 500;
  color: #fff;
}

// 内容滚动区域
.content-scroll {
  flex: 1;
  width: 100%;
  max-width: 100%;
  padding: 24rpx 32rpx 120rpx;
  box-sizing: border-box;
  overflow-x: hidden;
}

// 卡片样式
.section-card {
  width: 100%;
  max-width: 100%;
  background: #fff;
  border-radius: 24rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
  overflow: hidden;
  box-sizing: border-box;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 32rpx;
  background: linear-gradient(135deg, #f8f9fa 0%, #fff 100%);
  border-bottom: 1rpx solid #f0f0f0;
  width: 100%;
  box-sizing: border-box;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 16rpx;
}

.icon-wrapper {
  width: 48rpx;
  height: 48rpx;
  border-radius: 12rpx;
  display: flex;
  align-items: center;
  justify-content: center;

  &.customer-icon { background: linear-gradient(135deg, #4c6ef5, #6c5ce7); }
  &.device-icon { background: linear-gradient(135deg, #52c41a, #73d13d); }
  &.service-icon { background: linear-gradient(135deg, #faad14, #ffc53d); }
  &.parts-icon { background: linear-gradient(135deg, #722ed1, #9254de); }
  &.process-icon { background: linear-gradient(135deg, #13c2c2, #36cfc9); }
  &.record-icon { background: linear-gradient(135deg, #eb2f96, #f759ab); }
  &.equipment-icon { background: linear-gradient(135deg, #1890ff, #40a9ff); }
}

.section-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #262626;
}

.header-actions {
  display: flex;
  gap: 16rpx;
}

.action-btn {
  width: 48rpx;
  height: 48rpx;
  border-radius: 12rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;

  &.phone-btn { background: linear-gradient(135deg, #52c41a, #73d13d); }
  &.nav-btn { background: linear-gradient(135deg, #ff6b6b, #ff8a8a); }
  &.scan-btn { background: linear-gradient(135deg, #1890ff, #40a9ff); }
  &.add-btn { background: linear-gradient(135deg, #52c41a, #73d13d); }

  &:active {
    transform: scale(0.9);
  }
}

.card-body {
  padding: 32rpx;
  width: 100%;
  box-sizing: border-box;
}

// 客户信息样式
.customer-main {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24rpx;
  padding: 24rpx;
  background: #f8f9fa;
  border-radius: 16rpx;
}

.customer-name, .customer-phone {
  display: flex;
  align-items: center;
  gap: 12rpx;
}

.name-text {
  font-size: 30rpx;
  font-weight: 600;
  color: #262626;
}

.phone-text {
  font-size: 28rpx;
  color: #52c41a;
  font-weight: 500;
}

.customer-phone {
  cursor: pointer;
  transition: all 0.2s ease;

  &:active {
    opacity: 0.7;
  }
}

.address-section {
  margin-top: 8rpx;
}

.address-item {
  display: flex;
  align-items: flex-start;
  gap: 16rpx;
  padding: 24rpx;
  background: #f8f9fa;
  border-radius: 16rpx;
  cursor: pointer;
  transition: all 0.2s ease;

  &:active {
    background: #e9ecef;
  }
}

.address-icon {
  width: 32rpx;
  height: 32rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  margin-top: 4rpx;
}

.address-content {
  flex: 1;
  min-width: 0;
}

.address-main {
  font-size: 28rpx;
  color: #262626;
  line-height: 1.4;
  margin-bottom: 8rpx;
  display: block;
}

.address-detail {
  font-size: 26rpx;
  color: #8c8c8c;
  line-height: 1.3;
  display: block;
}

// 设备信息样式
.device-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20rpx;
  margin-bottom: 24rpx;
  width: 100%;
  max-width: 100%;
  box-sizing: border-box;
}

.device-item {
  display: flex;
  align-items: center;
  gap: 16rpx;
  padding: 24rpx;
  background: #f8f9fa;
  border-radius: 16rpx;
  width: 100%;
  max-width: 100%;
  box-sizing: border-box;
  overflow: hidden;
}

.device-icon-small {
  width: 32rpx;
  height: 32rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.device-info {
  flex: 1;
  min-width: 0;
}

.device-label {
  font-size: 24rpx;
  color: #8c8c8c;
  margin-bottom: 4rpx;
  display: block;
}

.device-value {
  font-size: 26rpx;
  color: #262626;
  font-weight: 500;
  display: block;
}

.tech-specs {
  padding: 24rpx;
  background: #f8f9fa;
  border-radius: 16rpx;
}

.specs-header {
  display: flex;
  align-items: center;
  gap: 12rpx;
  margin-bottom: 16rpx;
}

.specs-title {
  font-size: 26rpx;
  color: #262626;
  font-weight: 500;
}

.specs-content {
  font-size: 26rpx;
  color: #595959;
  line-height: 1.5;
}

// 服务内容样式
.service-info {
  display: flex;
  flex-direction: column;
  gap: 24rpx;
  width: 100%;
  max-width: 100%;
}

.service-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20rpx;
  width: 100%;
  max-width: 100%;
  box-sizing: border-box;
}

.service-item {
  display: flex;
  align-items: center;
  gap: 16rpx;
  padding: 24rpx;
  background: #f8f9fa;
  border-radius: 16rpx;
  width: 100%;
  max-width: 100%;
  box-sizing: border-box;
  overflow: hidden;
}

.service-icon-wrapper {
  width: 32rpx;
  height: 32rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.service-content {
  flex: 1;
  min-width: 0;
}

.service-label {
  font-size: 24rpx;
  color: #8c8c8c;
  margin-bottom: 4rpx;
  display: block;
}

.service-value {
  font-size: 26rpx;
  color: #262626;
  font-weight: 500;
  display: block;
}

.requirements-section, .remarks-section {
  padding: 24rpx;
  background: #f8f9fa;
  border-radius: 16rpx;
}

.requirements-header, .remarks-header {
  display: flex;
  align-items: center;
  gap: 12rpx;
  margin-bottom: 16rpx;
}

.requirements-title, .remarks-title {
  font-size: 26rpx;
  color: #262626;
  font-weight: 500;
}

.requirements-content, .remarks-content {
  font-size: 26rpx;
  color: #595959;
  line-height: 1.6;
}

// 使用耗材样式
.parts-container {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.part-card {
  background: #f8f9fa;
  border-radius: 16rpx;
  overflow: hidden;
}

.part-header {
  display: flex;
  align-items: center;
  gap: 16rpx;
  padding: 24rpx;
}

.part-icon {
  width: 32rpx;
  height: 32rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.part-main {
  flex: 1;
  min-width: 0;
}

.part-name {
  font-size: 28rpx;
  color: #262626;
  font-weight: 500;
  margin-bottom: 4rpx;
  display: block;
}

.part-spec {
  font-size: 24rpx;
  color: #8c8c8c;
  display: block;
}

.part-quantity {
  display: flex;
  align-items: baseline;
  gap: 4rpx;
  flex-shrink: 0;
}

.quantity-number {
  font-size: 32rpx;
  color: #722ed1;
  font-weight: 600;
}

.quantity-unit {
  font-size: 24rpx;
  color: #8c8c8c;
}

// 操作流程样式
.process-timeline {
  position: relative;
}

.timeline-item {
  display: flex;
  gap: 24rpx;
  padding-bottom: 32rpx;
  position: relative;

  &:not(.is-last)::after {
    content: '';
    position: absolute;
    left: 23rpx;
    top: 48rpx;
    bottom: 0;
    width: 2rpx;
    background: #e8e8e8;
  }

  &.is-completed::after {
    background: #52c41a;
  }

  &.is-active::after {
    background: linear-gradient(to bottom, #4c6ef5 0%, #e8e8e8 50%);
  }
}

.timeline-node {
  flex-shrink: 0;
  margin-top: 4rpx;
}

.node-icon {
  width: 48rpx;
  height: 48rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #e8e8e8;

  .is-completed & {
    background: #52c41a;
  }

  .is-active & {
    background: #4c6ef5;
  }
}

.node-number {
  font-size: 24rpx;
  font-weight: 600;
  color: #8c8c8c;

  .is-completed &, .is-active & {
    color: #fff;
  }
}

.timeline-content {
  flex: 1;
  min-width: 0;
}

.step-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 8rpx;
  gap: 16rpx;
}

.step-title {
  font-size: 28rpx;
  font-weight: 500;
  color: #262626;

  .is-completed & {
    color: #52c41a;
  }

  .is-active & {
    color: #4c6ef5;
  }
}

.step-time {
  display: flex;
  align-items: center;
  gap: 6rpx;
  font-size: 24rpx;
  color: #8c8c8c;
  flex-shrink: 0;
}

.step-desc {
  font-size: 26rpx;
  color: #8c8c8c;
  line-height: 1.4;
}

// 工作记录样式
.records-container {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.record-card {
  background: #f8f9fa;
  border-radius: 16rpx;
  padding: 24rpx;
}

.record-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
}

.record-type-badge {
  display: flex;
  align-items: center;
  gap: 8rpx;
  padding: 8rpx 16rpx;
  background: rgba(235, 47, 150, 0.1);
  border-radius: 20rpx;
}

.record-type {
  font-size: 24rpx;
  font-weight: 500;
  color: #eb2f96;
}

.record-time {
  display: flex;
  align-items: center;
  gap: 6rpx;
}

.time-text {
  font-size: 24rpx;
  color: #8c8c8c;
}

.record-content {
  font-size: 28rpx;
  color: #262626;
  line-height: 1.6;
  margin-bottom: 20rpx;
}

.record-images {
  display: flex;
  gap: 16rpx;
  flex-wrap: wrap;
}

.image-item {
  position: relative;
  width: 120rpx;
  height: 120rpx;
  border-radius: 12rpx;
  overflow: hidden;
  cursor: pointer;
}

.record-image {
  width: 100%;
  height: 100%;
}

.image-mask {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.4);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.image-item:active .image-mask {
  opacity: 1;
}

// 空状态样式
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60rpx 32rpx;
  gap: 16rpx;
}

.empty-icon {
  opacity: 0.6;
}

.empty-text {
  font-size: 26rpx;
  color: #8c8c8c;
}

.safe-bottom {
  height: 32rpx;
}

// 底部操作栏
.bottom-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  width: 100vw;
  max-width: 100vw;
  background: #fff;
  border-top: 1rpx solid #f0f0f0;
  padding: 24rpx 32rpx;
  padding-bottom: calc(24rpx + env(safe-area-inset-bottom));
  box-shadow: 0 -4rpx 20rpx rgba(0, 0, 0, 0.08);
  box-sizing: border-box;
}

.action-buttons {
  display: flex;
  gap: 16rpx;
  width: 100%;
  max-width: 100%;
  box-sizing: border-box;
}

.main-button, .secondary-button {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8rpx;
  height: 88rpx;
  border-radius: 24rpx;
  font-weight: 500;
  border: none;
  transition: all 0.3s ease;

  &:active {
    transform: scale(0.98);
  }
}

.main-button {
  flex: 1;
  color: #fff;

  &.accept-btn { background: linear-gradient(135deg, #52c41a, #73d13d); }
  &.start-btn { background: linear-gradient(135deg, #1890ff, #40a9ff); }
  &.complete-btn { background: linear-gradient(135deg, #52c41a, #73d13d); }
}

.secondary-button {
  min-width: 160rpx;
  background: #f8f9fa;
  border: 1rpx solid #e8e8e8;

  &:active {
    background: #e9ecef;
  }
}

.button-text {
  font-size: 28rpx;
  font-weight: 500;
}

// 设备统计样式
.equipment-summary {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.equipment-item {
  display: flex;
  align-items: center;
  gap: 16rpx;
  padding: 24rpx;
  background: #f8f9fa;
  border-radius: 16rpx;
  cursor: pointer;
  transition: all 0.2s ease;

  &:active {
    background: #e9ecef;
  }
}

.equipment-icon-wrapper {
  width: 48rpx;
  height: 48rpx;
  border-radius: 12rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;

  &.gas-cylinder {
    background: linear-gradient(135deg, #ff6b6b, #ff8a8a);
  }

  &.alarm {
    background: linear-gradient(135deg, #faad14, #ffc53d);
  }
}

.equipment-info {
  flex: 1;
  min-width: 0;
}

.equipment-name {
  font-size: 28rpx;
  color: #262626;
  font-weight: 500;
  margin-bottom: 4rpx;
  display: block;
}

.equipment-count {
  font-size: 24rpx;
  color: #8c8c8c;
  display: block;
}

.equipment-status {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 4rpx;
  flex-shrink: 0;
}

.normal-count {
  font-size: 24rpx;
  color: #52c41a;
  font-weight: 500;
}

.abnormal-count {
  font-size: 24rpx;
  color: #ff6b6b;
  font-weight: 500;
}
</style>
