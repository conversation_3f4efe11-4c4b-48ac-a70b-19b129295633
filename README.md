# 燃气管理系统 (Gas Management System)

一个现代化的燃气设备管理与维护平台，支持H5/微信小程序双平台部署。

## 🚀 项目特色

- **双平台支持**: 同时支持H5和微信小程序
- **角色权限管理**: 维保员、巡检员、配送员等多角色权限
- **移动优先设计**: 响应式设计，适配各种屏幕尺寸
- **现代化UI**: 基于Material Design 3.0设计语言
- **完整工作流**: 从任务接收到现场执行的完整闭环

## 📱 功能模块

### 🔐 用户认证系统
- 手机号验证登录
- 角色选择（维保员/巡检员/配送员）
- 权限管理
- 自动登录状态保持

### 🏠 工作台首页
- 个性化问候
- 今日数据概览
- 快速操作入口
- 最新任务展示
- 工作日程安排

### 📋 任务管理
- 任务列表查看
- 多维度筛选（状态、类型、优先级）
- 任务详情查看
- 任务接收/开始/完成
- 工作记录管理

### 🔧 现场执行
- GPS定位与导航
- 设备扫码识别
- 工作流程指导
- 参数测量记录
- 拍照上传
- 电子签名

### 👤 个人中心
- 个人信息管理
- 签到签退功能
- 工作数据统计
- 消息通知中心
- 应用设置

### 📚 知识库
- 技术手册
- 操作指南
- 常见问题FAQ
- 视频教程
- 内容搜索与收藏

## 🛠️ 技术栈

- **框架**: uni-app (Vue 3)
- **样式**: SCSS + Material Design 3.0
- **状态管理**: 自定义状态管理（无外部依赖）
- **构建工具**: HBuilderX
- **平台支持**: H5、微信小程序

## 📁 项目结构

```
src/
├── api/                    # API接口模块
│   └── index.js           # 统一API管理
├── pages/                  # 页面文件
│   ├── login/             # 登录页面
│   ├── dashboard/         # 工作台首页
│   ├── task/              # 任务管理
│   ├── maintenance/       # 现场执行
│   ├── profile/           # 个人中心
│   └── knowledge/         # 知识库
├── stores/                 # 状态管理
│   └── index.js           # 全局状态
├── styles/                 # 样式文件
│   └── global.scss        # 全局样式
├── utils/                  # 工具函数
│   └── index.js           # 通用工具
├── static/                 # 静态资源
│   └── icons/             # 图标文件
├── App.vue                # 应用入口
├── main.js                # 主入口文件
├── pages.json             # 页面配置
├── manifest.json          # 应用配置
└── uni.scss              # uni-app样式变量
```

## 🎨 设计系统

### 色彩规范
- **主色**: #1976D2 (蓝色)
- **辅助色**: #4CAF50 (绿色)、#FF9800 (橙色)、#F44336 (红色)
- **中性色**: #212121、#757575、#BDBDBD、#F5F5F5

### 间距规范
- **基础间距**: 16rpx
- **小间距**: 8rpx
- **大间距**: 24rpx
- **超大间距**: 32rpx

### 字体规范
- **标题**: 32rpx - 40rpx
- **正文**: 28rpx - 32rpx
- **辅助文字**: 24rpx - 26rpx

## 🚀 快速开始

### 环境要求
- HBuilderX 3.0+
- Node.js 14+
- 微信开发者工具（小程序开发）

### 安装步骤

1. **克隆项目**
```bash
git clone [项目地址]
cd gas-mgmt-system
```

2. **使用HBuilderX打开项目**
- 打开HBuilderX
- 文件 -> 打开目录 -> 选择项目文件夹

3. **运行项目**

**H5平台**:
- 点击运行 -> 运行到浏览器 -> Chrome

**微信小程序**:
- 点击运行 -> 运行到小程序模拟器 -> 微信开发者工具

### 配置说明

1. **API配置** (`src/api/index.js`)
```javascript
const BASE_URL = 'https://your-api-domain.com'
```

2. **应用配置** (`manifest.json`)
- 修改应用名称、版本号等信息
- 配置微信小程序AppID

## 📱 页面预览

### 登录页面
- 手机号验证
- 角色选择
- 现代化渐变设计

### 工作台首页
- 个性化问候
- 数据概览卡片
- 快速操作入口
- 任务列表预览

### 任务管理
- 搜索与筛选
- 任务状态管理
- 详情查看
- 操作流程

### 现场执行
- 位置信息显示
- 工作流程指导
- 工具箱集成
- 记录管理

## 🔧 开发指南

### 状态管理
项目使用自定义状态管理，无外部依赖：

```javascript
import store from '@/stores/index.js'

// 获取用户信息
const userInfo = store.state.user

// 更新用户信息
store.setUser({ name: '张三' })

// 登出
store.logout()
```

### API调用
```javascript
import { taskApi } from '@/api/index.js'

// 获取任务列表
const tasks = await taskApi.getTaskList({ status: 'new' })

// 接收任务
await taskApi.acceptTask(taskId)
```

### 工具函数
```javascript
import { formatDate, showToast, scanCode } from '@/utils/index.js'

// 格式化日期
const dateStr = formatDate(new Date(), 'YYYY-MM-DD')

// 显示提示
showToast('操作成功')

// 扫码功能
const result = await scanCode()
```

## 📋 开发进度

- [x] 项目架构设计与基础配置
- [x] 用户认证系统
- [x] 任务管理核心模块
- [x] 现场执行功能
- [x] 个人中心与工作台
- [x] 知识库与支持系统
- [ ] UI组件库与样式系统优化

